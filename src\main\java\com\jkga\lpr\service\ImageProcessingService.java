package com.jkga.lpr.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 图像处理服务 - 简化版本
 */
@Slf4j
@Service
public class ImageProcessingService {

    /**
     * 车牌区域信息
     */
    public static class PlateRegion {
        public int x, y, width, height;

        public PlateRegion(int x, int y, int width, int height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }

        public int area() {
            return width * height;
        }
    }

    /**
     * 图像预处理
     */
    public BufferedImage preprocessImage(BufferedImage originalImage) {
        // 转换为灰度图
        BufferedImage grayImage = new BufferedImage(
            originalImage.getWidth(),
            originalImage.getHeight(),
            BufferedImage.TYPE_BYTE_GRAY
        );

        Graphics2D g2d = grayImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();

        return grayImage;
    }

    /**
     * 车牌区域检测 - 简化版本（模拟检测）
     */
    public List<PlateRegion> detectPlateRegions(BufferedImage image) {
        List<PlateRegion> plateRegions = new ArrayList<>();

        try {
            int width = image.getWidth();
            int height = image.getHeight();

            // 模拟车牌检测 - 在图像中心区域假设存在车牌
            // 实际项目中这里应该使用真正的图像处理算法
            int plateWidth = Math.min(width / 3, 200);
            int plateHeight = Math.min(height / 6, 60);
            int plateX = (width - plateWidth) / 2;
            int plateY = (height - plateHeight) / 2;

            // 检查是否有足够的对比度（简单的亮度检测）
            if (hasEnoughContrast(image, plateX, plateY, plateWidth, plateHeight)) {
                plateRegions.add(new PlateRegion(plateX, plateY, plateWidth, plateHeight));
                log.debug("检测到车牌区域: x={}, y={}, width={}, height={}",
                    plateX, plateY, plateWidth, plateHeight);
            }

        } catch (Exception e) {
            log.error("车牌区域检测失败", e);
        }

        return plateRegions;
    }

    /**
     * 检查区域是否有足够的对比度
     */
    private boolean hasEnoughContrast(BufferedImage image, int x, int y, int width, int height) {
        try {
            int totalPixels = 0;
            int brightPixels = 0;

            for (int i = x; i < x + width && i < image.getWidth(); i++) {
                for (int j = y; j < y + height && j < image.getHeight(); j++) {
                    int rgb = image.getRGB(i, j);
                    int gray = (int) (0.299 * ((rgb >> 16) & 0xFF) +
                                     0.587 * ((rgb >> 8) & 0xFF) +
                                     0.114 * (rgb & 0xFF));
                    totalPixels++;
                    if (gray > 128) {
                        brightPixels++;
                    }
                }
            }

            // 如果亮像素比例在20%-80%之间，认为有足够对比度
            double brightRatio = (double) brightPixels / totalPixels;
            return brightRatio > 0.2 && brightRatio < 0.8;

        } catch (Exception e) {
            log.warn("对比度检测失败", e);
            return true; // 默认认为有对比度
        }
    }

    /**
     * 提取车牌区域图像
     */
    public BufferedImage extractPlateImage(BufferedImage originalImage, PlateRegion plateRegion) {
        try {
            // 扩展边界以包含完整车牌
            int padding = 10;
            int x = Math.max(0, plateRegion.x - padding);
            int y = Math.max(0, plateRegion.y - padding);
            int width = Math.min(originalImage.getWidth() - x, plateRegion.width + 2 * padding);
            int height = Math.min(originalImage.getHeight() - y, plateRegion.height + 2 * padding);

            return originalImage.getSubimage(x, y, width, height);
        } catch (Exception e) {
            log.error("提取车牌区域失败", e);
            return originalImage;
        }
    }

    /**
     * 车牌图像增强处理
     */
    public BufferedImage enhancePlateImage(BufferedImage plateImage) {
        try {
            // 转换为灰度图并增强对比度
            BufferedImage enhanced = new BufferedImage(
                plateImage.getWidth(),
                plateImage.getHeight(),
                BufferedImage.TYPE_BYTE_GRAY
            );

            Graphics2D g2d = enhanced.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(plateImage, 0, 0, null);
            g2d.dispose();

            return enhanced;
        } catch (Exception e) {
            log.error("车牌图像增强失败", e);
            return plateImage;
        }
    }

    /**
     * 调整图像大小
     */
    public BufferedImage resizeImage(BufferedImage image, int maxWidth, int maxHeight) {
        if (image.getWidth() <= maxWidth && image.getHeight() <= maxHeight) {
            return image;
        }

        double scaleX = (double) maxWidth / image.getWidth();
        double scaleY = (double) maxHeight / image.getHeight();
        double scale = Math.min(scaleX, scaleY);

        int newWidth = (int) (image.getWidth() * scale);
        int newHeight = (int) (image.getHeight() * scale);

        BufferedImage resized = new BufferedImage(newWidth, newHeight, image.getType());
        Graphics2D g2d = resized.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(image, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        return resized;
    }

    /**
     * 从文件路径加载图像
     */
    public BufferedImage loadImage(String imagePath) {
        try {
            BufferedImage image = ImageIO.read(new File(imagePath));
            if (image == null) {
                throw new RuntimeException("无法加载图像: " + imagePath);
            }
            return image;
        } catch (IOException e) {
            throw new RuntimeException("加载图像失败: " + imagePath, e);
        }
    }

    /**
     * 保存图像到文件
     */
    public boolean saveImage(BufferedImage image, String outputPath) {
        try {
            String format = outputPath.substring(outputPath.lastIndexOf('.') + 1);
            return ImageIO.write(image, format, new File(outputPath));
        } catch (IOException e) {
            log.error("保存图像失败: {}", outputPath, e);
            return false;
        }
    }
}
