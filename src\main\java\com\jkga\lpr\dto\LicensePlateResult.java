package com.jkga.lpr.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车牌识别结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LicensePlateResult {
    
    /**
     * 车牌号码
     */
    private String plateNumber;
    
    /**
     * 识别置信度 (0-1)
     */
    private Double confidence;
    
    /**
     * 车牌类型 (蓝牌、黄牌、绿牌等)
     */
    private String plateType;
    
    /**
     * 车牌区域坐标
     */
    private PlateRegion region;
    
    /**
     * 处理时间(毫秒)
     */
    private Long processingTime;
    
    /**
     * 车牌区域坐标信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlateRegion {
        private Integer x;
        private Integer y;
        private Integer width;
        private Integer height;
    }
}
