package com.jkga.lpr.controller;

import com.jkga.lpr.dto.ApiResponse;
import com.jkga.lpr.dto.LicensePlateResult;
import com.jkga.lpr.service.LicensePlateRecognitionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 车牌识别控制器
 */
@RestController
@RequestMapping("/v1/license-plate")
public class LicensePlateController {

    private static final Logger log = LoggerFactory.getLogger(LicensePlateController.class);

    @Autowired
    private LicensePlateRecognitionService licensePlateRecognitionService;

    /**
     * 车牌识别接口
     */
    @PostMapping(value = "/recognize", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "车牌识别", description = "上传车牌图片进行识别")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "识别成功",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "请求参数错误"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "服务器内部错误"
        )
    })
    public ApiResponse<LicensePlateResult> recognizeLicensePlate(
            @Parameter(description = "车牌图片文件", required = true)
            @RequestParam("image") MultipartFile imageFile) {
        
        log.info("接收到车牌识别请求，文件名: {}, 大小: {} bytes", 
                imageFile.getOriginalFilename(), imageFile.getSize());
        
        try {
            LicensePlateResult result = licensePlateRecognitionService.recognizeLicensePlate(imageFile);
            
            log.info("车牌识别完成，结果: {}, 置信度: {}, 处理时间: {}ms", 
                    result.getPlateNumber(), result.getConfidence(), result.getProcessingTime());
            
            return ApiResponse.success(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("车牌识别请求参数错误: {}", e.getMessage());
            return ApiResponse.error(400, e.getMessage());
            
        } catch (Exception e) {
            log.error("车牌识别处理失败", e);
            return ApiResponse.error(500, "车牌识别处理失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查服务是否正常运行")
    public ApiResponse<String> healthCheck() {
        return ApiResponse.success("车牌识别服务运行正常");
    }

    /**
     * 获取支持的图片格式
     */
    @GetMapping("/supported-formats")
    @Operation(summary = "获取支持的图片格式", description = "返回系统支持的图片文件格式")
    public ApiResponse<String[]> getSupportedFormats() {
        String[] formats = {"jpg", "jpeg", "png", "bmp"};
        return ApiResponse.success(formats);
    }
}
