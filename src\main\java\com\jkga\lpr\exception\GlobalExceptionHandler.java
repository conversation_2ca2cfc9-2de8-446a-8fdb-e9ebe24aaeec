package com.jkga.lpr.exception;

import com.jkga.lpr.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理车牌识别业务异常
     */
    @ExceptionHandler(LicensePlateRecognitionException.class)
    public ResponseEntity<ApiResponse<Void>> handleLicensePlateRecognitionException(
            LicensePlateRecognitionException e) {
        log.error("车牌识别业务异常: {}", e.getMessage(), e);
        
        ApiResponse<Void> response = ApiResponse.error(e.getErrorCode(), e.getMessage());
        HttpStatus status = HttpStatus.valueOf(e.getErrorCode());
        
        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Void>> handleIllegalArgumentException(
            IllegalArgumentException e) {
        log.warn("参数校验异常: {}", e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.error(400, e.getMessage());
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse<Void>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException e) {
        log.warn("文件上传大小超限: {}", e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.error(400, "上传文件大小超过限制");
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理文件上传异常
     */
    @ExceptionHandler(MultipartException.class)
    public ResponseEntity<ApiResponse<Void>> handleMultipartException(MultipartException e) {
        log.warn("文件上传异常: {}", e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.error(400, "文件上传失败: " + e.getMessage());
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Void>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        
        ApiResponse<Void> response = ApiResponse.error(500, "系统内部错误");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleException(Exception e) {
        log.error("未处理的异常: {}", e.getMessage(), e);
        
        ApiResponse<Void> response = ApiResponse.error(500, "系统发生未知错误");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
