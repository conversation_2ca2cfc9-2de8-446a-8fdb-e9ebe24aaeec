package com.jkga.lpr.controller;

import com.jkga.lpr.dto.LicensePlateResult;
import com.jkga.lpr.service.LicensePlateRecognitionService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 车牌识别控制器测试
 */
@WebMvcTest(LicensePlateController.class)
class LicensePlateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private LicensePlateRecognitionService licensePlateRecognitionService;

    @Test
    void testRecognizeLicensePlate_Success() throws Exception {
        // 准备测试数据
        LicensePlateResult mockResult = LicensePlateResult.builder()
                .plateNumber("京A12345")
                .confidence(0.95)
                .plateType("普通车牌")
                .processingTime(1000L)
                .build();

        when(licensePlateRecognitionService.recognizeLicensePlate(any()))
                .thenReturn(mockResult);

        // 创建模拟文件
        MockMultipartFile mockFile = new MockMultipartFile(
                "image",
                "test-plate.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content".getBytes()
        );

        // 执行测试
        mockMvc.perform(multipart("/v1/license-plate/recognize")
                        .file(mockFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.plateNumber").value("京A12345"))
                .andExpect(jsonPath("$.data.confidence").value(0.95))
                .andExpect(jsonPath("$.data.plateType").value("普通车牌"));
    }

    @Test
    void testRecognizeLicensePlate_InvalidFile() throws Exception {
        when(licensePlateRecognitionService.recognizeLicensePlate(any()))
                .thenThrow(new IllegalArgumentException("不支持的文件格式"));

        MockMultipartFile mockFile = new MockMultipartFile(
                "image",
                "test.txt",
                MediaType.TEXT_PLAIN_VALUE,
                "not an image".getBytes()
        );

        mockMvc.perform(multipart("/v1/license-plate/recognize")
                        .file(mockFile))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("不支持的文件格式"));
    }

    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/v1/license-plate/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("车牌识别服务运行正常"));
    }

    @Test
    void testGetSupportedFormats() throws Exception {
        mockMvc.perform(get("/v1/license-plate/supported-formats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0]").value("jpg"));
    }
}
