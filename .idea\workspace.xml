<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="302979c5-1f3c-4707-afaf-599e89bfd109" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="D:/maven/apache-maven-3.5.4" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.5.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="31FyD0hjzL3gYR035z7AI2oBQic" />
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/pom.xml" />
    <property name="settings.editor.selected.configurable" value="MavenSettings" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="LicensePlateRecognitionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="license-plate-recognition" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.jkga.lpr.LicensePlateRecognitionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="302979c5-1f3c-4707-afaf-599e89bfd109" name="Default Changelist" comment="" />
      <created>1755133991408</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755133991408</updated>
      <workItem from="1755133994731" duration="379000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
</project>