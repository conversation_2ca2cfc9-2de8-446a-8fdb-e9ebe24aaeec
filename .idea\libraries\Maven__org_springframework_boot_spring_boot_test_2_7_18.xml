<component name="libraryTable">
  <library name="Maven: org.springframework.boot:spring-boot-test:2.7.18">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/boot/spring-boot-test/2.7.18/spring-boot-test-2.7.18.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/boot/spring-boot-test/2.7.18/spring-boot-test-2.7.18-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/boot/spring-boot-test/2.7.18/spring-boot-test-2.7.18-sources.jar!/" />
    </SOURCES>
  </library>
</component>