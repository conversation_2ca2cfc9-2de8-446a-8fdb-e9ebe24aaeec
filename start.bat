@echo off
echo Starting License Plate Recognition System...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install JDK 17 or higher
    pause
    exit /b 1
)

REM Check if <PERSON>ven is installed
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: <PERSON>ven is not installed or not in PATH
    echo Please install Maven 3.6 or higher
    pause
    exit /b 1
)

echo Building project...
mvn clean compile

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Starting application...
echo Access the API at: http://localhost:8080/api
echo Access Swagger UI at: http://localhost:8080/api/swagger-ui.html
echo.
echo Press Ctrl+C to stop the application
echo.

mvn spring-boot:run

pause
