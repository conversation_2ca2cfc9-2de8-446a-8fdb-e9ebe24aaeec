# 车牌识别系统 (License Plate Recognition System)

基于Spring Boot和OpenCV的智能车牌识别系统，支持中国车牌的自动检测和识别。

## 功能特性

- 🚗 **智能车牌检测**: 使用OpenCV进行车牌区域自动定位
- 🔍 **高精度OCR识别**: 集成Tesseract OCR引擎，支持中英文字符识别
- 📱 **RESTful API**: 提供简洁的HTTP接口，支持图片上传和结果返回
- 🎯 **多种车牌类型**: 支持普通车牌、新能源车牌、警用车牌等
- 📊 **置信度评估**: 提供识别结果的置信度评分
- 🛡️ **异常处理**: 完善的错误处理和参数验证
- 📖 **API文档**: 集成Swagger UI，提供交互式API文档

## 技术栈

- **后端框架**: Spring Boot 3.2.0
- **图像处理**: OpenCV 4.8.0
- **OCR引擎**: Tesseract 5.3.0
- **构建工具**: Maven
- **Java版本**: JDK 17
- **API文档**: SpringDoc OpenAPI 3

## 快速开始

### 环境要求

- JDK 17 或更高版本
- Maven 3.6 或更高版本
- Tesseract OCR (可选，用于更好的识别效果)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd LincensePlateRecognitio
   ```

2. **安装依赖**
   ```bash
   mvn clean install
   ```

3. **配置Tesseract (可选)**
   - 下载并安装Tesseract OCR
   - 下载中文语言包 (chi_sim.traineddata)
   - 配置 `application.yml` 中的 `lpr.ocr.tessdata-path`

4. **启动应用**
   ```bash
   mvn spring-boot:run
   ```

5. **访问应用**
   - API服务: http://localhost:8080/api
   - Swagger文档: http://localhost:8080/api/swagger-ui.html

## API使用说明

### 车牌识别接口

**POST** `/api/v1/license-plate/recognize`

**请求参数:**
- `image`: 车牌图片文件 (支持 jpg, jpeg, png, bmp 格式)

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "plateNumber": "京A12345",
    "confidence": 0.95,
    "plateType": "普通车牌",
    "region": {
      "x": 100,
      "y": 50,
      "width": 200,
      "height": 60
    },
    "processingTime": 1500
  },
  "timestamp": 1703123456789
}
```

### 健康检查接口

**GET** `/api/v1/license-plate/health`

### 支持格式查询

**GET** `/api/v1/license-plate/supported-formats`

## 配置说明

主要配置项在 `src/main/resources/application.yml`:

```yaml
lpr:
  # 临时文件存储路径
  temp-dir: ${java.io.tmpdir}/lpr
  # 支持的图片格式
  supported-formats: jpg,jpeg,png,bmp
  # OCR配置
  ocr:
    tessdata-path: ${user.home}/tessdata
    language: chi_sim+eng
  # 图像处理参数
  image:
    max-width: 1920
    max-height: 1080
```

## 测试

运行单元测试:
```bash
mvn test
```

运行集成测试:
```bash
mvn verify
```

## 使用示例

### cURL示例

```bash
curl -X POST \
  http://localhost:8080/api/v1/license-plate/recognize \
  -H 'Content-Type: multipart/form-data' \
  -F 'image=@/path/to/your/license-plate.jpg'
```

### Java客户端示例

```java
// 使用Spring RestTemplate
RestTemplate restTemplate = new RestTemplate();
MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
body.add("image", new FileSystemResource("/path/to/image.jpg"));

HttpHeaders headers = new HttpHeaders();
headers.setContentType(MediaType.MULTIPART_FORM_DATA);

HttpEntity<MultiValueMap<String, Object>> requestEntity = 
    new HttpEntity<>(body, headers);

ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
    "http://localhost:8080/api/v1/license-plate/recognize",
    requestEntity,
    ApiResponse.class
);
```

## 性能优化建议

1. **图片预处理**: 上传前适当压缩图片可提高处理速度
2. **内存管理**: 大批量处理时注意OpenCV Mat对象的释放
3. **并发处理**: 可配置线程池处理多个识别请求
4. **缓存策略**: 对相同图片可实现结果缓存

## 常见问题

### Q: 识别准确率不高怎么办？
A: 
- 确保图片清晰，车牌区域占比适中
- 检查光照条件，避免过曝或过暗
- 考虑安装完整版Tesseract和中文语言包

### Q: 启动时OpenCV加载失败？
A: 
- 检查系统架构是否匹配
- 尝试更新OpenCV版本
- 查看详细错误日志

### Q: 支持哪些车牌类型？
A: 目前支持中国大陆标准车牌，包括：
- 普通蓝牌、黄牌
- 新能源绿牌
- 警用、军用车牌
- 使领馆车牌

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: JKGA Team
- 邮箱: <EMAIL>
