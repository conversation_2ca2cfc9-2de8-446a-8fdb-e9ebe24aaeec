<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
    <facet type="web" name="Web">
      <configuration>
        <webroots />
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
        </sourceRoots>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.12" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.30" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.83" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.83" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-validation:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.83" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.2.5.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.validation:jakarta.validation-api:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.4.3.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-imaging:1.0.0-alpha5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.12.0" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.11.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.30" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-ui:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-webmvc-core:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-common:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-core:2.2.9" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-annotations:2.2.9" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-models:2.2.9" level="project" />
    <orderEntry type="library" name="Maven: org.webjars:swagger-ui:4.18.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.7.18" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.7.18" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.7.18" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.7.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.4.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:2.4.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:9.3" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: jakarta.activation:jakarta.activation-api:1.2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.22.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest:2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-api:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.opentest4j:opentest4j:1.2.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-commons:1.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.apiguardian:apiguardian-api:1.1.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-params:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-engine:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-engine:1.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:4.5.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy:1.12.23" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.12.23" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:3.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-junit-jupiter:4.5.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.3.31" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.3.31" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.9.1" level="project" />
  </component>
</module>