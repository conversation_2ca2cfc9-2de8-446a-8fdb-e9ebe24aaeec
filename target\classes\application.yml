server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: license-plate-recognition
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

# 车牌识别配置
lpr:
  # 临时文件存储路径
  temp-dir: ${java.io.tmpdir}/lpr
  # 支持的图片格式
  supported-formats: jpg,jpeg,png,bmp
  # OCR配置
  ocr:
    # Tesseract数据路径（需要下载中文语言包）
    tessdata-path: ${user.home}/tessdata
    # 识别语言
    language: chi_sim+eng
  # 图像处理参数
  image:
    # 最大宽度
    max-width: 1920
    # 最大高度  
    max-height: 1080

logging:
  level:
    com.jkga.lpr: DEBUG
    org.opencv: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
