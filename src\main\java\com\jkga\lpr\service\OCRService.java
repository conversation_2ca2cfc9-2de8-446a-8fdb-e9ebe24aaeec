package com.jkga.lpr.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * OCR识别服务 - 简化版本（模拟识别）
 */
@Slf4j
@Service
public class OCRService {

    // 中国车牌号码正则表达式
    private static final Pattern PLATE_PATTERN = Pattern.compile(
        "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$"
    );

    // 模拟车牌号码库
    private static final String[] SAMPLE_PLATES = {
        "京A12345", "沪B67890", "粤C11111", "川D22222", "鲁E33333",
        "苏F44444", "浙G55555", "闽H66666", "赣J77777", "湘K88888",
        "鄂L99999", "豫M00000", "晋N12345", "陕O67890", "甘P11111"
    };

    private final Random random = new Random();

    /**
     * 识别车牌号码 - 模拟识别
     */
    public String recognizePlateNumber(BufferedImage plateImage) {
        try {
            // 模拟OCR识别过程
            log.debug("开始模拟OCR识别，图像尺寸: {}x{}", plateImage.getWidth(), plateImage.getHeight());

            // 基于图像特征选择车牌号码（这里简化为随机选择）
            String plateNumber = generateMockPlateNumber(plateImage);

            log.debug("模拟OCR识别结果: {}", plateNumber);

            return plateNumber;

        } catch (Exception e) {
            log.error("OCR识别失败", e);
            return "";
        }
    }

    /**
     * 基于图像特征生成模拟车牌号码
     */
    private String generateMockPlateNumber(BufferedImage image) {
        // 计算图像的简单特征值
        int width = image.getWidth();
        int height = image.getHeight();
        int area = width * height;

        // 基于图像特征选择车牌号码（确保相同图像返回相同结果）
        int index = Math.abs((width * height + width + height) % SAMPLE_PLATES.length);

        return SAMPLE_PLATES[index];
    }

    /**
     * 清理OCR识别结果
     */
    private String cleanOCRResult(String rawText) {
        if (rawText == null || rawText.trim().isEmpty()) {
            return "";
        }
        
        // 移除空白字符和特殊字符
        String cleaned = rawText.replaceAll("\\s+", "")
                                .replaceAll("[^\\u4e00-\\u9fa5A-Za-z0-9]", "")
                                .toUpperCase();
        
        // 字符替换（常见OCR错误修正）
        cleaned = cleaned.replace("0", "O")  // 数字0替换为字母O
                        .replace("1", "I")   // 数字1替换为字母I
                        .replace("8", "B");  // 数字8替换为字母B（在某些位置）
        
        // 验证车牌格式
        if (isValidPlateNumber(cleaned)) {
            return cleaned;
        }
        
        // 如果不符合标准格式，尝试修正
        return attemptPlateCorrection(cleaned);
    }

    /**
     * 验证车牌号码格式
     */
    private boolean isValidPlateNumber(String plateNumber) {
        return PLATE_PATTERN.matcher(plateNumber).matches();
    }

    /**
     * 尝试修正车牌号码
     */
    private String attemptPlateCorrection(String plateNumber) {
        if (plateNumber.length() < 7 || plateNumber.length() > 8) {
            return plateNumber; // 长度不对，无法修正
        }
        
        StringBuilder corrected = new StringBuilder(plateNumber);
        
        // 第一位应该是中文省份简称
        if (corrected.length() > 0 && !isChineseProvince(corrected.charAt(0))) {
            // 尝试常见的OCR错误修正
            char firstChar = corrected.charAt(0);
            if (firstChar == 'B') corrected.setCharAt(0, '京');
            else if (firstChar == 'H') corrected.setCharAt(0, '沪');
            // 可以添加更多修正规则
        }
        
        // 第二位应该是字母
        if (corrected.length() > 1) {
            char secondChar = corrected.charAt(1);
            if (Character.isDigit(secondChar)) {
                // 数字转字母的常见错误
                if (secondChar == '0') corrected.setCharAt(1, 'O');
                else if (secondChar == '1') corrected.setCharAt(1, 'I');
            }
        }
        
        return corrected.toString();
    }

    /**
     * 判断是否为中文省份简称
     */
    private boolean isChineseProvince(char c) {
        String provinces = "京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领";
        return provinces.indexOf(c) != -1;
    }

    /**
     * 获取识别置信度（简单实现）
     */
    public double getConfidence(String plateNumber) {
        if (plateNumber == null || plateNumber.isEmpty()) {
            return 0.0;
        }
        
        // 基于车牌格式正确性计算置信度
        if (isValidPlateNumber(plateNumber)) {
            return 0.9; // 格式正确，高置信度
        } else if (plateNumber.length() >= 7 && plateNumber.length() <= 8) {
            return 0.6; // 长度正确但格式可能有问题
        } else {
            return 0.3; // 低置信度
        }
    }
}
