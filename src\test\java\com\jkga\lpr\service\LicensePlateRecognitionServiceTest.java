package com.jkga.lpr.service;

import com.jkga.lpr.dto.LicensePlateResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 车牌识别服务测试
 */
@ExtendWith(MockitoExtension.class)
class LicensePlateRecognitionServiceTest {

    @Mock
    private ImageProcessingService imageProcessingService;

    @Mock
    private OCRService ocrService;

    private LicensePlateRecognitionService licensePlateRecognitionService;

    @BeforeEach
    void setUp() {
        licensePlateRecognitionService = new LicensePlateRecognitionService(
                imageProcessingService, ocrService);
        
        // 设置配置属性
        ReflectionTestUtils.setField(licensePlateRecognitionService, "tempDir", System.getProperty("java.io.tmpdir"));
        ReflectionTestUtils.setField(licensePlateRecognitionService, "supportedFormats", "jpg,jpeg,png,bmp");
        ReflectionTestUtils.setField(licensePlateRecognitionService, "maxWidth", 1920);
        ReflectionTestUtils.setField(licensePlateRecognitionService, "maxHeight", 1080);
    }

    @Test
    void testValidateImageFile_ValidFile() {
        MockMultipartFile validFile = new MockMultipartFile(
                "image",
                "test.jpg",
                "image/jpeg",
                "test content".getBytes()
        );

        // 应该不抛出异常
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(licensePlateRecognitionService, 
                    "validateImageFile", validFile);
        });
    }

    @Test
    void testValidateImageFile_EmptyFile() {
        MockMultipartFile emptyFile = new MockMultipartFile(
                "image",
                "test.jpg",
                "image/jpeg",
                new byte[0]
        );

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(licensePlateRecognitionService, 
                    "validateImageFile", emptyFile);
        });

        assertEquals("图像文件不能为空", exception.getMessage());
    }

    @Test
    void testValidateImageFile_UnsupportedFormat() {
        MockMultipartFile unsupportedFile = new MockMultipartFile(
                "image",
                "test.gif",
                "image/gif",
                "test content".getBytes()
        );

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(licensePlateRecognitionService, 
                    "validateImageFile", unsupportedFile);
        });

        assertTrue(exception.getMessage().contains("不支持的文件格式"));
    }

    @Test
    void testDeterminePlateType() {
        // 测试普通车牌
        String plateType1 = ReflectionTestUtils.invokeMethod(
                licensePlateRecognitionService, "determinePlateType", "京A12345");
        assertEquals("普通车牌", plateType1);

        // 测试新能源车牌
        String plateType2 = ReflectionTestUtils.invokeMethod(
                licensePlateRecognitionService, "determinePlateType", "京AD12345");
        assertEquals("新能源车牌", plateType2);

        // 测试警用车牌
        String plateType3 = ReflectionTestUtils.invokeMethod(
                licensePlateRecognitionService, "determinePlateType", "京A1234警");
        assertEquals("警用车牌", plateType3);
    }

    @Test
    void testGetFileExtension() {
        String extension1 = ReflectionTestUtils.invokeMethod(
                licensePlateRecognitionService, "getFileExtension", "test.jpg");
        assertEquals("jpg", extension1);

        String extension2 = ReflectionTestUtils.invokeMethod(
                licensePlateRecognitionService, "getFileExtension", "image.PNG");
        assertEquals("PNG", extension2);

        String extension3 = ReflectionTestUtils.invokeMethod(
                licensePlateRecognitionService, "getFileExtension", "noextension");
        assertEquals("", extension3);
    }
}
