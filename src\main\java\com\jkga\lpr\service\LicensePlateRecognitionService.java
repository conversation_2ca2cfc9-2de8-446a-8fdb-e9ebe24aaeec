package com.jkga.lpr.service;

import com.jkga.lpr.dto.LicensePlateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import java.awt.image.BufferedImage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

/**
 * 车牌识别主服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LicensePlateRecognitionService {

    private final ImageProcessingService imageProcessingService;
    private final OCRService ocrService;

    @Value("${lpr.temp-dir}")
    private String tempDir;

    @Value("${lpr.supported-formats}")
    private String supportedFormats;

    @Value("${lpr.image.max-width:1920}")
    private int maxWidth;

    @Value("${lpr.image.max-height:1080}")
    private int maxHeight;

    /**
     * 识别车牌
     */
    public LicensePlateResult recognizeLicensePlate(MultipartFile imageFile) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证文件
            validateImageFile(imageFile);
            
            // 保存临时文件
            String tempFilePath = saveTempFile(imageFile);
            
            try {
                // 加载图像
                BufferedImage originalImage = imageProcessingService.loadImage(tempFilePath);

                // 调整图像大小
                BufferedImage resizedImage = imageProcessingService.resizeImage(originalImage, maxWidth, maxHeight);

                // 图像预处理
                BufferedImage processedImage = imageProcessingService.preprocessImage(resizedImage);

                // 检测车牌区域
                List<ImageProcessingService.PlateRegion> plateRegions = imageProcessingService.detectPlateRegions(processedImage);

                if (plateRegions.isEmpty()) {
                    log.warn("未检测到车牌区域");
                    return buildEmptyResult(System.currentTimeMillis() - startTime);
                }

                // 选择最佳车牌区域（选择面积最大的）
                ImageProcessingService.PlateRegion bestPlateRegion = selectBestPlateRegion(plateRegions);

                // 提取车牌图像
                BufferedImage plateImage = imageProcessingService.extractPlateImage(resizedImage, bestPlateRegion);

                // 车牌图像增强
                BufferedImage enhancedPlateImage = imageProcessingService.enhancePlateImage(plateImage);

                // OCR识别
                String plateNumber = ocrService.recognizePlateNumber(enhancedPlateImage);
                double confidence = ocrService.getConfidence(plateNumber);
                
                // 构建结果
                return LicensePlateResult.builder()
                        .plateNumber(plateNumber)
                        .confidence(confidence)
                        .plateType(determinePlateType(plateNumber))
                        .region(LicensePlateResult.PlateRegion.builder()
                                .x(bestPlateRegion.x)
                                .y(bestPlateRegion.y)
                                .width(bestPlateRegion.width)
                                .height(bestPlateRegion.height)
                                .build())
                        .processingTime(System.currentTimeMillis() - startTime)
                        .build();
                        
            } finally {
                // 清理临时文件
                cleanupTempFile(tempFilePath);
            }
            
        } catch (Exception e) {
            log.error("车牌识别失败", e);
            throw new RuntimeException("车牌识别处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证图像文件
     */
    private void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("图像文件不能为空");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!supportedFormats.contains(extension)) {
            throw new IllegalArgumentException("不支持的文件格式: " + extension + 
                "，支持的格式: " + supportedFormats);
        }
        
        // 检查文件大小（10MB限制在配置中已设置）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }
    }

    /**
     * 保存临时文件
     */
    private String saveTempFile(MultipartFile file) throws IOException {
        // 创建临时目录
        Path tempDirPath = Paths.get(tempDir);
        if (!Files.exists(tempDirPath)) {
            Files.createDirectories(tempDirPath);
        }
        
        // 生成唯一文件名
        String extension = getFileExtension(file.getOriginalFilename());
        String tempFileName = UUID.randomUUID().toString() + "." + extension;
        String tempFilePath = Paths.get(tempDir, tempFileName).toString();
        
        // 保存文件
        file.transferTo(new File(tempFilePath));
        
        log.debug("临时文件已保存: {}", tempFilePath);
        return tempFilePath;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(String filePath) {
        try {
            Files.deleteIfExists(Paths.get(filePath));
            log.debug("临时文件已清理: {}", filePath);
        } catch (IOException e) {
            log.warn("清理临时文件失败: {}", filePath, e);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 选择最佳车牌区域
     */
    private ImageProcessingService.PlateRegion selectBestPlateRegion(List<ImageProcessingService.PlateRegion> plateRegions) {
        return plateRegions.stream()
                .max((r1, r2) -> Integer.compare(r1.area(), r2.area()))
                .orElse(plateRegions.get(0));
    }

    /**
     * 确定车牌类型
     */
    private String determinePlateType(String plateNumber) {
        if (plateNumber == null || plateNumber.length() < 2) {
            return "未知";
        }
        
        // 根据车牌号码特征判断类型
        if (plateNumber.contains("新能源") || plateNumber.length() == 8) {
            return "新能源车牌";
        } else if (plateNumber.matches(".*[使领].*")) {
            return "使领馆车牌";
        } else if (plateNumber.matches(".*[警].*")) {
            return "警用车牌";
        } else if (plateNumber.matches(".*[学].*")) {
            return "教练车牌";
        } else {
            return "普通车牌";
        }
    }

    /**
     * 构建空结果
     */
    private LicensePlateResult buildEmptyResult(long processingTime) {
        return LicensePlateResult.builder()
                .plateNumber("")
                .confidence(0.0)
                .plateType("未识别")
                .processingTime(processingTime)
                .build();
    }
}
