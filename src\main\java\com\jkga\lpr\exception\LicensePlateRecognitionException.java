package com.jkga.lpr.exception;

/**
 * 车牌识别业务异常
 */
public class LicensePlateRecognitionException extends RuntimeException {

    private final int errorCode;

    public LicensePlateRecognitionException(String message) {
        super(message);
        this.errorCode = 500;
    }

    public LicensePlateRecognitionException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public LicensePlateRecognitionException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = 500;
    }

    public LicensePlateRecognitionException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }
}
